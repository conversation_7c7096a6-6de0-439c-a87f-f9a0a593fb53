<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <title>Vertigo AMS - Modernized Admin Navigation</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Vertigo AMS Brand Colors */
        :root {
            --primary-500: #0068ff;
            --primary-600: #0056d6;
            --primary-700: #0045ad;
            --brand-800: #243b53;
            --brand-900: #003472;
            --success-500: #10b981;
            --warning-500: #f59e0b;
            --danger-500: #ef4444;
        }

        .gradient-primary {
            background: linear-gradient(135deg, #0068ff 0%, #0056d6 100%);
        }
        
        .gradient-brand {
            background: linear-gradient(135deg, #243b53 0%, #003472 100%);
        }

        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }

        .nav-shadow {
            box-shadow: 0 4px 20px rgba(0, 104, 255, 0.1);
        }

        .admin-sidebar {
            transition: width 0.3s ease;
        }

        .admin-sidebar.collapsed {
            width: 4rem;
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            .admin-sidebar {
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 50;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 280px !important;
                max-width: 85vw;
            }

            .admin-sidebar.mobile-open {
                transform: translateX(0);
            }

            .admin-content {
                margin-left: 0 !important;
            }
        }

        /* Prevent horizontal scroll on small screens */
        @media (max-width: 640px) {
            .admin-sidebar {
                width: 100vw !important;
                max-width: 100vw !important;
            }
        }

        /* Safe area support for devices with notches */
        .safe-area-padding {
            padding-left: max(1rem, env(safe-area-inset-left));
            padding-right: max(1rem, env(safe-area-inset-right));
        }

        .safe-area-top {
            padding-top: max(1rem, env(safe-area-inset-top));
        }

        /* Improve mobile scrolling */
        .mobile-scroll {
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: contain;
        }

        /* Better mobile tap highlights */
        .mobile-touch-target {
            min-height: 44px;
            min-width: 44px;
            -webkit-tap-highlight-color: rgba(0, 104, 255, 0.1);
        }

        .menu-item {
            transition: all 0.2s ease;
        }

        .menu-item:hover {
            transform: translateX(4px);
        }

        /* Enhanced menu animations */
        .menu-item-active {
            background: linear-gradient(135deg, #0068ff 0%, #0056d6 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(0, 104, 255, 0.3);
        }

        .submenu-enter-active, .submenu-leave-active {
            transition: all 0.3s ease;
        }

        .submenu-enter-from, .submenu-leave-to {
            opacity: 0;
            transform: translateY(-10px);
        }

        /* Badge styles */
        .nav-badge {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        /* Tooltip styles */
        .tooltip {
            position: absolute;
            z-index: 1000;
            padding: 0.5rem 0.75rem;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s ease;
        }

        .tooltip.show {
            opacity: 1;
        }

        /* Status indicators */
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .status-online { background-color: var(--success-500); }
        .status-warning { background-color: var(--warning-500); }
        .status-offline { background-color: var(--danger-500); }
    </style>
</head>
<body class="bg-gray-50">
    <div id="app">
        <div class="flex h-screen bg-gray-100 overflow-hidden">
            <!-- Mobile Overlay -->
            <div
                v-if="adminMobileMenuOpen"
                @click="closeAdminMobileMenu"
                class="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
            ></div>

            <!-- Admin Sidebar -->
            <div :class="[
                'admin-sidebar bg-white border-r border-gray-200 flex flex-col z-50 shadow-xl',
                sidebarCollapsed ? 'w-16' : 'w-72',
                'md:relative md:translate-x-0',
                adminMobileMenuOpen ? 'mobile-open' : ''
            ]">
                <!-- Admin Header -->
                <div class="p-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
                    <div class="flex items-center justify-between">
                        <div v-if="!sidebarCollapsed" class="flex items-center">
                            <div class="w-12 h-12 mr-4">
                                <svg viewBox="0 0 260 260" class="w-full h-full drop-shadow-sm">
                                    <circle fill="#243b53" cx="130" cy="130" r="130"/>
                                    <path fill="#fff" d="M184.38,167.7h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
                                    <path fill="#fff" d="M125.73,128.23v15.55h-13.22l2.79,3.7-2.79,4.66h13.22v15.56h-40.82v-15.54l.74-1.24,2.21-3.7-2.95-3.87v-10.93l2.51-4.2h38.31Z"/>
                                    <path fill="#fff" d="M184.38,123.37h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
                                    <path fill="#fff" d="M96.65,123.37v-20.91h-12.65v-18.55h44.35v18.55h-12.65v20.91h-19.04Z"/>
                                    <polygon fill="#fff" points="227.59 173.63 224 172.2 223.61 169.86 221.71 171.29 218.13 169.86 224.52 164.98 226.31 165.69 227.59 173.63"/>
                                    <polygon fill="#fff" points="31.03 173.63 34.62 172.2 35 169.86 36.9 171.29 40.48 169.86 34.1 164.98 32.3 165.69 31.03 173.63"/>
                                </svg>
                            </div>
                            <div class="hidden sm:block">
                                <h1 class="text-xl font-bold text-gray-900">Trust Actioneers</h1>
                                <p class="text-sm text-gray-500 flex items-center">
                                    <span class="status-indicator status-online"></span>
                                    Admin Panel
                                </p>
                            </div>
                        </div>
                        <div v-else class="w-12 h-12 mx-auto relative">
                            <svg viewBox="0 0 260 260" class="w-full h-full drop-shadow-sm">
                                <circle fill="#243b53" cx="130" cy="130" r="130"/>
                                <path fill="#fff" d="M184.38,167.7h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
                                <path fill="#fff" d="M125.73,128.23v15.55h-13.22l2.79,3.7-2.79,4.66h13.22v15.56h-40.82v-15.54l.74-1.24,2.21-3.7-2.95-3.87v-10.93l2.51-4.2h38.31Z"/>
                                <path fill="#fff" d="M184.38,123.37h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
                                <path fill="#fff" d="M96.65,123.37v-20.91h-12.65v-18.55h44.35v18.55h-12.65v20.91h-19.04Z"/>
                                <polygon fill="#fff" points="227.59 173.63 224 172.2 223.61 169.86 221.71 171.29 218.13 169.86 224.52 164.98 226.31 165.69 227.59 173.63"/>
                                <polygon fill="#fff" points="31.03 173.63 34.62 172.2 35 169.86 36.9 171.29 40.48 169.86 34.1 164.98 32.3 165.69 31.03 173.63"/>
                            </svg>
                            <div class="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                        </div>
                        <button
                            @click="toggleSidebar"
                            class="hidden md:block p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200"
                        >
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="sidebarCollapsed ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Admin Navigation Menu -->
                <nav class="flex-1 p-4 overflow-y-auto mobile-scroll">
                    <div class="space-y-2">
                        <!-- Dashboard -->
                        <div class="menu-item">
                            <a href="/home" :class="[
                                'flex items-center px-4 py-3 rounded-xl font-medium mobile-touch-target transition-all duration-200',
                                currentPage === 'dashboard' ? 'menu-item-active text-white' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                            ]">
                                <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                </svg>
                                <span v-if="!sidebarCollapsed">Dashboard</span>
                                <span v-if="!sidebarCollapsed && notifications.dashboard > 0" class="ml-auto px-2 py-1 text-xs bg-red-500 text-white rounded-full nav-badge">
                                    {{ notifications.dashboard }}
                                </span>
                            </a>
                        </div>

                        <!-- Sales -->
                        <div class="menu-item">
                            <a href="/orders" :class="[
                                'flex items-center px-4 py-3 rounded-xl font-medium mobile-touch-target transition-all duration-200',
                                currentPage === 'sales' ? 'menu-item-active text-white' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                            ]">
                                <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                </svg>
                                <span v-if="!sidebarCollapsed">Sales</span>
                                <span v-if="!sidebarCollapsed && notifications.sales > 0" class="ml-auto px-2 py-1 text-xs bg-green-500 text-white rounded-full">
                                    {{ notifications.sales }}
                                </span>
                            </a>
                        </div>

                        <!-- Auctions Section -->
                        <div class="menu-item">
                            <button
                                @click="toggleMenu('auctions')"
                                :class="[
                                    'w-full flex items-center justify-between px-4 py-3 rounded-xl font-medium transition-all duration-200 mobile-touch-target',
                                    openMenus.auctions ? 'bg-primary-50 text-primary-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                                ]"
                            >
                                <div class="flex items-center">
                                    <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                    <span v-if="!sidebarCollapsed">Auctions</span>
                                </div>
                                <svg v-if="!sidebarCollapsed" :class="['h-4 w-4 transition-transform duration-200', openMenus.auctions ? 'rotate-180' : '']" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <transition name="submenu">
                                <div v-if="openMenus.auctions && !sidebarCollapsed" class="ml-8 mt-2 space-y-1">
                                    <a href="/auctions" class="block px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg mobile-touch-target transition-colors duration-200">Auction List</a>
                                    <a href="/auction-types" class="block px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg mobile-touch-target transition-colors duration-200">Auction Categories</a>
                                    <a href="/auction-listing/create" class="block px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg mobile-touch-target transition-colors duration-200">Create Auction</a>
                                </div>
                            </transition>
                        </div>

                        <!-- Items Section -->
                        <div class="menu-item">
                            <button
                                @click="toggleMenu('items')"
                                :class="[
                                    'w-full flex items-center justify-between px-4 py-3 rounded-xl font-medium transition-all duration-200 mobile-touch-target',
                                    openMenus.items ? 'bg-primary-50 text-primary-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                                ]"
                            >
                                <div class="flex items-center">
                                    <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                    <span v-if="!sidebarCollapsed">Items</span>
                                </div>
                                <svg v-if="!sidebarCollapsed" :class="['h-4 w-4 transition-transform duration-200', openMenus.items ? 'rotate-180' : '']" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <transition name="submenu">
                                <div v-if="openMenus.items && !sidebarCollapsed" class="ml-8 mt-2 space-y-1">
                                    <a href="/items" class="block px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg mobile-touch-target transition-colors duration-200">Auction Items</a>
                                    <a href="/items/create" class="block px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg mobile-touch-target transition-colors duration-200">Add Item</a>
                                </div>
                            </transition>
                        </div>

                        <!-- Adverts -->
                        <div class="menu-item">
                            <a href="/adverts" :class="[
                                'flex items-center px-4 py-3 rounded-xl font-medium mobile-touch-target transition-all duration-200',
                                currentPage === 'adverts' ? 'menu-item-active text-white' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                            ]">
                                <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                </svg>
                                <span v-if="!sidebarCollapsed">Adverts</span>
                            </a>
                        </div>

                        <!-- Deposits -->
                        <div class="menu-item">
                            <a href="/transactions" :class="[
                                'flex items-center px-4 py-3 rounded-xl font-medium mobile-touch-target transition-all duration-200',
                                currentPage === 'deposits' ? 'menu-item-active text-white' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                            ]">
                                <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                                <span v-if="!sidebarCollapsed">Deposits</span>
                                <span v-if="!sidebarCollapsed && notifications.deposits > 0" class="ml-auto px-2 py-1 text-xs bg-blue-500 text-white rounded-full">
                                    {{ notifications.deposits }}
                                </span>
                            </a>
                        </div>

                        <!-- Section Divider -->
                        <div v-if="!sidebarCollapsed" class="px-4 py-2">
                            <div class="border-t border-gray-200"></div>
                        </div>

                        <!-- Reports Section -->
                        <div class="menu-item">
                            <button
                                @click="toggleMenu('reports')"
                                :class="[
                                    'w-full flex items-center justify-between px-4 py-3 rounded-xl font-medium transition-all duration-200 mobile-touch-target',
                                    openMenus.reports ? 'bg-primary-50 text-primary-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                                ]"
                            >
                                <div class="flex items-center">
                                    <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    <span v-if="!sidebarCollapsed">Reports</span>
                                </div>
                                <svg v-if="!sidebarCollapsed" :class="['h-4 w-4 transition-transform duration-200', openMenus.reports ? 'rotate-180' : '']" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <transition name="submenu">
                                <div v-if="openMenus.reports && !sidebarCollapsed" class="ml-8 mt-2 space-y-1">
                                    <a href="/winners-report" class="block px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg mobile-touch-target transition-colors duration-200">Winners Report</a>
                                    <a href="/sales-report" class="block px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg mobile-touch-target transition-colors duration-200">Sales Report</a>
                                    <a href="/inventory-report" class="block px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg mobile-touch-target transition-colors duration-200">Inventory Report</a>
                                    <a href="/refund-list-report" class="block px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg mobile-touch-target transition-colors duration-200">Refund List</a>
                                    <a href="/deposits-report" class="block px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg mobile-touch-target transition-colors duration-200">Deposits Report</a>
                                </div>
                            </transition>
                        </div>

                        <!-- User Management Section -->
                        <div class="menu-item">
                            <button
                                @click="toggleMenu('users')"
                                :class="[
                                    'w-full flex items-center justify-between px-4 py-3 rounded-xl font-medium transition-all duration-200 mobile-touch-target',
                                    openMenus.users ? 'bg-primary-50 text-primary-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                                ]"
                            >
                                <div class="flex items-center">
                                    <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                    <span v-if="!sidebarCollapsed">User Management</span>
                                </div>
                                <svg v-if="!sidebarCollapsed" :class="['h-4 w-4 transition-transform duration-200', openMenus.users ? 'rotate-180' : '']" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <transition name="submenu">
                                <div v-if="openMenus.users && !sidebarCollapsed" class="ml-8 mt-2 space-y-1">
                                    <a href="/users" class="block px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg mobile-touch-target transition-colors duration-200">Users</a>
                                    <a href="/roles" class="block px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg mobile-touch-target transition-colors duration-200">Roles</a>
                                    <a href="/statuses" class="block px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg mobile-touch-target transition-colors duration-200">Status</a>
                                </div>
                            </transition>
                        </div>

                        <!-- Settings -->
                        <div class="menu-item">
                            <a href="/settings" :class="[
                                'flex items-center px-4 py-3 rounded-xl font-medium mobile-touch-target transition-all duration-200',
                                currentPage === 'settings' ? 'menu-item-active text-white' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                            ]">
                                <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span v-if="!sidebarCollapsed">Settings</span>
                            </a>
                        </div>
                    </div>
                </nav>

                <!-- Admin User Info -->
                <div class="p-4 border-t border-gray-200 bg-gradient-to-r from-gray-50 to-white">
                    <div v-if="!sidebarCollapsed" class="flex items-center">
                        <div class="h-12 w-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mr-3 shadow-lg">
                            <span class="text-white font-bold text-lg">AD</span>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-semibold text-gray-900">Admin User</p>
                            <p class="text-xs text-gray-500 flex items-center">
                                <span class="status-indicator status-online"></span>
                                Super Administrator
                            </p>
                        </div>
                        <div class="flex space-x-1">
                            <button class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors duration-200" title="Notifications">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                </svg>
                            </button>
                            <button class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors duration-200" title="Logout">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div v-else class="flex justify-center">
                        <div class="h-12 w-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center shadow-lg relative">
                            <span class="text-white font-bold text-lg">AD</span>
                            <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Admin Main Content -->
            <div class="flex-1 flex flex-col overflow-hidden admin-content">
                <!-- Admin Top Bar -->
                <div class="bg-white border-b border-gray-200 px-4 sm:px-6 py-4 shadow-sm">
                    <div class="flex items-center justify-between">
                        <!-- Mobile menu button -->
                        <button
                            @click="toggleAdminMobileMenu"
                            class="md:hidden p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
                        >
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>

                        <div class="flex-1 md:flex-none">
                            <h2 class="text-xl sm:text-2xl font-bold text-gray-900">{{ pageTitle }}</h2>
                            <p class="text-sm text-gray-500 hidden sm:block">{{ pageSubtitle }}</p>
                        </div>

                        <div class="flex items-center space-x-2 sm:space-x-4">
                            <!-- Quick Actions -->
                            <button class="hidden sm:flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                <span class="hidden lg:inline">Create New</span>
                                <span class="lg:hidden">Create</span>
                            </button>

                            <!-- Notifications -->
                            <button class="relative p-2 text-gray-400 hover:text-gray-500 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 8A6 6 0 006 8c0 7-3 9-3 9h18s-3-2-3-9z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.73 21a2 2 0 01-3.46 0"></path>
                                </svg>
                                <span class="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold shadow-lg">8</span>
                            </button>

                            <!-- Search -->
                            <div class="relative hidden lg:block">
                                <input type="text" placeholder="Search..." class="w-48 xl:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200">
                                <svg class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>

                            <!-- Mobile Search Button -->
                            <button class="lg:hidden p-2 text-gray-400 hover:text-gray-500 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Admin Dashboard Content -->
                <div class="flex-1 p-4 sm:p-6 bg-gray-50 overflow-y-auto">
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
                        <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500 mb-2">Active Auctions</h3>
                                    <p class="text-3xl font-bold text-gray-900">24</p>
                                    <p class="text-sm text-green-600 mt-1 flex items-center">
                                        <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                        </svg>
                                        +12% from last month
                                    </p>
                                </div>
                                <div class="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500 mb-2">Total Revenue</h3>
                                    <p class="text-3xl font-bold text-gray-900">$45,231</p>
                                    <p class="text-sm text-green-600 mt-1 flex items-center">
                                        <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                        </svg>
                                        +8% from last month
                                    </p>
                                </div>
                                <div class="h-12 w-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500 mb-2">Registered Users</h3>
                                    <p class="text-3xl font-bold text-gray-900">1,429</p>
                                    <p class="text-sm text-green-600 mt-1 flex items-center">
                                        <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                        </svg>
                                        +23% from last month
                                    </p>
                                </div>
                                <div class="h-12 w-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500 mb-2">Items Listed</h3>
                                    <p class="text-3xl font-bold text-gray-900">156</p>
                                    <p class="text-sm text-red-600 mt-1 flex items-center">
                                        <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                                        </svg>
                                        -3% from last month
                                    </p>
                                </div>
                                <div class="h-12 w-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity and System Status -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <svg class="h-5 w-5 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Recent Auctions
                            </h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                                    <div class="flex items-center">
                                        <div class="h-10 w-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                                            <span class="text-white font-bold text-sm">VW</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">Vintage Watch Collection</p>
                                            <p class="text-sm text-gray-500">Ends in 2 hours</p>
                                        </div>
                                    </div>
                                    <span class="text-green-600 font-semibold">$1,250</span>
                                </div>
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                                    <div class="flex items-center">
                                        <div class="h-10 w-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                                            <span class="text-white font-bold text-sm">AD</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">Art Deco Furniture</p>
                                            <p class="text-sm text-gray-500">Ends in 1 day</p>
                                        </div>
                                    </div>
                                    <span class="text-green-600 font-semibold">$850</span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <svg class="h-5 w-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                System Status
                            </h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600 flex items-center">
                                        <span class="status-indicator status-online"></span>
                                        Server Status
                                    </span>
                                    <span class="text-green-600 font-medium">Online</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600 flex items-center">
                                        <span class="status-indicator status-online"></span>
                                        Database
                                    </span>
                                    <span class="text-green-600 font-medium">Connected</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600 flex items-center">
                                        <span class="status-indicator status-online"></span>
                                        Payment Gateway
                                    </span>
                                    <span class="text-green-600 font-medium">Active</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600 flex items-center">
                                        <span class="status-indicator status-warning"></span>
                                        Email Service
                                    </span>
                                    <span class="text-yellow-600 font-medium">Limited</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
