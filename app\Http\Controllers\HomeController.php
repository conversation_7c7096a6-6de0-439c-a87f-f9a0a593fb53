<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Facades\App\Cache\Repo;
use Facades\App\Libraries\AuctionHandler;


class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */


    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        return view('home');
    }

    /**
     * Show the modernized admin dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function modernizedAdmin()
    {
        // Get dashboard statistics
        $stats = [
            'active_auctions' => \App\Models\AuctionType::where('type', 'live')->count(),
            'total_revenue' => array_sum(Repo::getAnnualSales()),
            'total_users' => Repo::getStaffCursor()->count() + Repo::getCustomerCursor()->count(),
            'total_items' => \App\Models\Item::count(),
        ];

        // Get recent auctions for the dashboard
        $recentAuctions = \App\Models\AuctionType::with('items')
            ->where('type', 'live')
            ->take(5)
            ->get()
            ->map(function ($auction) {
                return (object) [
                    'title' => $auction->name,
                    'status' => 'Active',
                    'current_bid' => $auction->items->sum('reserve_price') ?? 0,
                ];
            });

        // Get notification count (you can customize this logic)
        $notificationCount = 8; // Example count

        return view('admin.modernized-dashboard', compact('stats', 'recentAuctions', 'notificationCount'));
    }

    /**
     * Show the admin interface comparison page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function adminComparison()
    {
        return view('admin.comparison');
    }
    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function welcome()
    {
        // Redirect authenticated non-customer users to admin dashboard
        if( auth()->check() && ! auth()->user()->is_customer ) {
            return redirect('/home');
        }

        // Handle AJAX requests for items
        if( request()->ajax() ) {
            try {
                $items = Repo::getItems();
                return response()->json($items);
            } catch (\Exception $e) {
                return response()->json(['error' => 'Failed to load items'], 500);
            }
        }

        // Load welcome page with necessary data
        try {
            $auctionTypes = \App\Models\AuctionType::with('items')->get();
            $branches = \App\Models\Branch::all();

            return view('welcome', compact('auctionTypes', 'branches'));
        } catch (\Exception $e) {
            // Fallback if there are database issues
            return view('welcome');
        }
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function shop()
    {

        if( request()->ajax() ) {
            $items = Repo::getItems();
            return response($items);
        }

        return view('welcome');
    }
    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function about()
    {
        return view('frontend.about');
    }

    public function refundList(){
        $auctions = Repo::refundList();
        return view("app.reports.refund-list-report", compact("auctions"));
    }

    public function winnersReport(){
        $auctions = Repo::winnersReport();
        return view("app.reports.winners-report", compact("auctions")); 
    }
    
    public function salesReport(){
        $items = Repo::salesReport();
        return view("app.reports.sales-report", compact("items"));
    }
    
    public function inventoryReport(){
        $items = Repo::inventoryReport();
        return view("app.reports.inventory-report", compact("items"));
    }    

    public function refundListReport (){
        $transactions = Repo::refundListReport();
        return view("app.reports.refund-list-report", compact("transactions"));
    }

    public function depositsReport (){
        $transactions = Repo::depositsReport();
        return view("app.reports.deposit-report", compact("transactions"));
    }


    public function notifications(Request $request) {
        return view("app.notifications.index");
    }

    // Route::get('refund-list-report', [HomeController::class, 'refundListReport']);
    // Route::get('winners-report', [HomeController::class, 'winnersReport']);
    // Route::get('sales-report', [HomeController::class, 'salesReport']);
    // Route::get('inventory-report', [HomeController::class, 'inventoryReport']);
    // Route::get('deposits-report', [HomeController::class, 'depositsReport']);




}
