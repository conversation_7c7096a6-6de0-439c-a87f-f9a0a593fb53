<?php $__env->startSection('title', 'Admin Interface Comparison - Vertigo AMS'); ?>

<?php $__env->startSection('page-title', 'Interface Comparison'); ?>
<?php $__env->startSection('page-subtitle', 'Compare the original and modernized admin interfaces'); ?>

<?php $__env->startSection('quick-actions'); ?>
<div class="flex space-x-2">
    <a href="<?php echo e(route('home')); ?>" class="flex items-center bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        <span class="hidden lg:inline">Original Admin</span>
        <span class="lg:hidden">Original</span>
    </a>
    <a href="<?php echo e(route('admin.modernized')); ?>" class="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
        </svg>
        <span class="hidden lg:inline">Modernized Admin</span>
        <span class="lg:hidden">Modern</span>
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Comparison Overview -->
<div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 mb-8">
    <div class="flex items-start">
        <div class="flex-shrink-0">
            <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
        </div>
        <div class="ml-4">
            <h3 class="text-2xl font-bold text-blue-900 mb-2">🎨 Admin Interface Comparison</h3>
            <p class="text-blue-800 mb-4">Compare the original admin interface with the new modernized version. Both interfaces maintain 100% functionality parity while the modernized version offers enhanced user experience.</p>
            <div class="flex flex-wrap gap-2">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Same Functionality
                </span>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                    Mobile Responsive
                </span>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 21h10a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v7z"></path>
                    </svg>
                    Modern Design
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Feature Comparison Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Original Interface -->
    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <svg class="h-5 w-5 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Original Admin Interface
            </h3>
            <a href="<?php echo e(route('home')); ?>" class="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors duration-200">
                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                </svg>
                View Original
            </a>
        </div>
        <div class="space-y-4">
            <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                    <div class="h-2 w-2 bg-gray-400 rounded-full"></div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Bootstrap-based Design</p>
                    <p class="text-sm text-gray-500">Traditional admin layout with sidebar navigation</p>
                </div>
            </div>
            <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                    <div class="h-2 w-2 bg-gray-400 rounded-full"></div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Complete Functionality</p>
                    <p class="text-sm text-gray-500">All existing features and permissions</p>
                </div>
            </div>
            <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                    <div class="h-2 w-2 bg-gray-400 rounded-full"></div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Familiar Interface</p>
                    <p class="text-sm text-gray-500">Current production interface users know</p>
                </div>
            </div>
            <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                    <div class="h-2 w-2 bg-yellow-400 rounded-full"></div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Limited Mobile Support</p>
                    <p class="text-sm text-gray-500">Basic responsive design</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Modernized Interface -->
    <div class="bg-white rounded-xl p-6 shadow-sm border border-primary-200 bg-gradient-to-br from-white to-primary-50">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <svg class="h-5 w-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                Modernized Admin Interface
            </h3>
            <a href="<?php echo e(route('admin.modernized')); ?>" class="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-primary-100 text-primary-800 hover:bg-primary-200 transition-colors duration-200">
                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                </svg>
                View Modern
            </a>
        </div>
        <div class="space-y-4">
            <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                    <div class="h-2 w-2 bg-green-500 rounded-full"></div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Tailwind CSS Design</p>
                    <p class="text-sm text-gray-500">Modern, clean interface with smooth animations</p>
                </div>
            </div>
            <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                    <div class="h-2 w-2 bg-green-500 rounded-full"></div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">100% Functionality Parity</p>
                    <p class="text-sm text-gray-500">All features preserved with enhanced UX</p>
                </div>
            </div>
            <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                    <div class="h-2 w-2 bg-green-500 rounded-full"></div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Mobile-First Design</p>
                    <p class="text-sm text-gray-500">Fully responsive with touch-friendly interface</p>
                </div>
            </div>
            <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                    <div class="h-2 w-2 bg-green-500 rounded-full"></div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Enhanced Features</p>
                    <p class="text-sm text-gray-500">Collapsible sidebar, tooltips, status indicators</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Navigation Comparison -->
<div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 mb-8">
    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <svg class="h-5 w-5 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h2M9 5a2 2 0 012 2v10a2 2 0 01-2 2M9 5a2 2 0 012-2h2a2 2 0 012 2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
        </svg>
        Navigation Structure Comparison
    </h3>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Menu Item</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Original</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Modernized</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enhancement</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Dashboard</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">✅ Available</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">✅ Available</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">Enhanced stats cards</td>
                </tr>
                <tr class="bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Sales</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">✅ Available</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">✅ Available</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">Notification badges</td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Auctions</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">✅ Available</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">✅ Available</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">Expandable submenu</td>
                </tr>
                <tr class="bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Items</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">✅ Available</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">✅ Available</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">Organized submenu</td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Reports</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">✅ Available</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">✅ Available</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">All 5 reports grouped</td>
                </tr>
                <tr class="bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">User Management</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">✅ Available</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">✅ Available</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">Grouped organization</td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Settings</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">✅ Available</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">✅ Available</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">Enhanced styling</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Implementation Status -->
<div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <svg class="h-5 w-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Implementation Status
    </h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="text-center">
            <div class="h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <h4 class="text-lg font-semibold text-gray-900">Ready for Production</h4>
            <p class="text-sm text-gray-500 mt-1">Modernized interface is fully functional and ready to replace the original</p>
        </div>
        <div class="text-center">
            <div class="h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                </svg>
            </div>
            <h4 class="text-lg font-semibold text-gray-900">Gradual Migration</h4>
            <p class="text-sm text-gray-500 mt-1">Both interfaces can run simultaneously during transition period</p>
        </div>
        <div class="text-center">
            <div class="h-16 w-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <h4 class="text-lg font-semibold text-gray-900">Zero Downtime</h4>
            <p class="text-sm text-gray-500 mt-1">Switch between interfaces without affecting system operations</p>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.modernized-admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/admin/comparison.blade.php ENDPATH**/ ?>